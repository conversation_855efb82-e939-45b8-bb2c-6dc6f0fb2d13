// this for search any city 
// const button = document.getElementById("Search button");
// const input = document.getElementById("city");

// const cityname = document.getElementById("city_name");
// const temp = document.getElementById("city_temp");
// const citytime = document.getElementById("cityTime");

// jS for getting an time 
// const button = document.getElementById("get_location");

// async function getdata(lat,lon){
//     const promise = await fetch(`http://api.weatherapi.com/v1/current.json?key=77485222f1ff470ab0d93451250207&q=${lat},${lon}&aqi=yes`);
//     return await promise.json();

// }
// async function gotLocation(position){
//     const result = await getdata(position.coords.latitude,position.coords.longitude);
//     console.log(result);
// }
// function failedToGet(error){
//     console.log(error);
// }

// button.addEventListener("click",async () =>{
//     navigator.geolocation.getCurrentPosition(gotLocation,failedToGet)
// })

// async function getdata(cityName){
//     const promise = await fetch(`http://api.weatherapi.com/v1/current.json?key=77485222f1ff470ab0d93451250207&q=${input.value}&aqi=yes`);
//     return await promise.json();

// }
// button.addEventListener("click",async () =>{
//     const value = input.value;
//     const result = await getdata(value);
//     console.log(result);
//     cityname.innerText = `${result.location.name},${result.location.country},${result.location.region}`;
//     temp.innerText = `${result.current.temp_c}`;
//     citytime.innerText = `${result.location.localtime}`;

// })
const myname = document.getElementById("my_name");
const button = document.getElementById("my_btn");

function maketextsizer(size){
    function changeSize(){
        myname.style.fontsize = `$(size)px`;
    }
    return changeSize;
}
const sizeOf20 = maketextsizer(20);
const sizeOf30 = maketextsizer(30);
const sizeOf80 = maketextsizer(80);
const sizeOf10 = maketextsizer(10);

button.addEventListener("click", sizeOf20);