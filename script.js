// const button = document.getElementById("Search button");
// const input = document.getElementById("city");

// const cityname = document.getElementById("city_name");
// const temp = document.getElementById("city_temp");
// const citytime = document.getElementById("cityTime");
const button = document.getElementById("get_location");
function gotLocation(position){
    console.log(position);
}
function failedToGet(error){
    console.log(error);
}

button.addEventListener("click",async () =>{
    navigator.geolocation.getCurrentPosition(gotLocation,failedToGet)
})


// async function getdata(cityName){
//     const promise = await fetch(`http://api.weatherapi.com/v1/current.json?key=77485222f1ff470ab0d93451250207&q=${input.value}&aqi=yes`);
//     return await promise.json();
// }
// button.addEventListener("click",async () =>{
//     const value = input.value;
//     const result = await getdata(value);
//     console.log(result);
//     cityname.innerText = `${result.location.name},${result.location.country},${result.location.region}`;
//     temp.innerText = `${result.current.temp_c}`;
//     citytime.innerText = `${result.location.localtime}`;

// })